import 'package:bloc/bloc.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/entities/calendar_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_calendar_usecase.dart';
import 'package:storetrack_app/shared/models/result.dart';

import '../../../domain/entities/tasks_request_entity.dart';
import '../../../domain/entities/tasks_response_entity.dart';
import '../../../domain/usecases/get_tasks_usecase.dart';
import 'today_page_state.dart';

class TodayPageCubit extends Cubit<TodayPageState> {
  final GetTasksUseCase _getTasksUseCase;
  final GetCalendarUseCase _getCalendarUseCase;

  TodayPageCubit(this._getTasksUseCase, this._getCalendarUseCase)
      : super(TodayPageInitial());

  Future<void> getData(TasksRequestEntity request) async {
    emit(TodayPageLoading());

    try {
      final results = await Future.wait([
        _getTasksUseCase(request),
        _getCalendarUseCase(GetCalendarParams(
          token: request.token,
          userId: request.userId,
        )),
      ]);

      final tasksResult = results[0] as Result<TasksResponseEntity>;
      final calendarResult = results[1] as Result<CalendarResponseEntity>;

      final operationNames = ['Tasks', 'Calendar'];

      // We need to check hasFailures on the individual results now
      if (!tasksResult.isSuccess || !calendarResult.isSuccess) {
        // Combine error messages if necessary, or handle individually
        // For simplicity, we'll just emit an error if either fails
        final errorMessage = tasksResult.error?.toString() ??
            calendarResult.error?.toString() ??
            'Unknown error occurred.';
        emit(TodayPageError(errorMessage));
        return;
      }

      // Debug log for calendar data
      logger('Calendar API result success: ${calendarResult.isSuccess}');
      if (calendarResult.isSuccess && calendarResult.data != null) {
        logger('Calendar data available: ${calendarResult.data != null}');
        if (calendarResult.data?.data != null) {
          logger(
              'Calendar info available: ${calendarResult.data?.data?.calendarInfo != null}');
          if (calendarResult.data?.data?.calendarInfo != null) {
            logger(
                'Calendar info count: ${calendarResult.data?.data?.calendarInfo?.length}');

            // Log some calendar info entries for debugging
            final calendarInfo = calendarResult.data?.data?.calendarInfo;
            if (calendarInfo != null && calendarInfo.isNotEmpty) {
              for (var i = 0; i < calendarInfo.length && i < 5; i++) {
                var info = calendarInfo[i];
                logger(
                    'Calendar info $i: timestamp=${info.timestamp}, dollarSymbol=${info.dollarSymbol}, budgetAmount=${info.budgetAmount}');
              }
            }
          }
        }
      } else if (!calendarResult.isSuccess) {
        logger('Calendar API error: ${calendarResult.error}');
      }

      // All succeeded, extract data
      logger('Emitting TodayPageSuccess with calendar data');
      emit(TodayPageSuccess(
        tasksResponse: tasksResult.data!,
        calendarResponse: calendarResult.data,
      ));
    } catch (e) {
      logger("Unexpected error in TodayPageCubit: $e");
      emit(TodayPageError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  void resetState() {
    emit(TodayPageInitial());
  }
}
