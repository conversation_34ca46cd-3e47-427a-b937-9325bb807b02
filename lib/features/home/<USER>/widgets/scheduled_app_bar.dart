import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';

class ScheduledAppBar extends StatelessWidget implements PreferredSizeWidget {
  final bool isCheckboxMode;
  final VoidCallback onCalendarTap;
  final VoidCallback onEditTap;
  final PreferredSizeWidget bottom;

  const ScheduledAppBar({
    super.key,
    required this.isCheckboxMode,
    required this.onCalendarTap,
    required this.onEditTap,
    required this.bottom,
  });

  @override
  Widget build(BuildContext context) {
    return CustomAppBar(
      title: 'Scheduled',
      bottom: bottom,
      actions: [
        GestureDetector(
          onTap: onCalendarTap,
          child: Image.asset(
            AppAssets.appbarCalendar,
            scale: 4,
            color: !isCheckboxMode ? AppColors.primaryBlue : AppColors.black,
          ),
        ),
        const Gap(8),
        GestureDetector(
          onTap: onEditTap,
          child: Image.asset(
            AppAssets.appbarCalendarEdit,
            color: isCheckboxMode ? AppColors.primaryBlue : AppColors.black,
            scale: 4,
          ),
        ),
        const Gap(8),
        GestureDetector(
          onTap: () {
            openRouteMap();
            // context.router.push(const JourneyMapRoute());
          },
          child: Image.asset(
            AppAssets.appbarMap,
            scale: 4,
          ),
        ),
        const Gap(16)
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight + 48);
}
